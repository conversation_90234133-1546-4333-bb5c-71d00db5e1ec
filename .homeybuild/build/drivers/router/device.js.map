{"version": 3, "file": "device.js", "sourceRoot": "", "sources": ["../../../drivers/router/device.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,+DAA2G;AAE3G,MAAM,YAAa,SAAQ,eAAK,CAAC,MAAM;IAAvC;;QACU,WAAM,GAA2B,IAAI,CAAC;QACtC,yBAAoB,GAA0B,IAAI,CAAC;QAC1C,2BAAsB,GAAW,KAAK,CAAC,CAAC,WAAW;IA2VtE,CAAC;IAzVC;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAE/C,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEjD,2BAA2B;QAC3B,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QACvC,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;SACpD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;SAC1D;QAED,gCAAgC;QAChC,IAAI,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,mDAAmD;QACnD,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,KAAK,EAAE;gBACT,sDAAsD;gBACtD,OAAO;aACR;iBAAM;gBACL,qBAAqB;gBACrB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;aACtB;QACH,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACrB,+BAA+B;QACjC,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC/B,+BAA+B;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;YAE1C,8FAA8F;YAC9F,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACvC,IAAI,CAAC,GAAG,CAAC,gFAAgF,CAAC,CAAC;gBAC3F,QAAQ,GAAG,aAAa,CAAC;gBAEzB,uDAAuD;gBACvD,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAClD,IAAI,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;aAC3D;YAED,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,eAAe,QAAQ,IAAI,SAAS,eAAe,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;YAE1H,oBAAoB;YACpB,IAAI,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,GAAG,IAAI,iCAAe,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC1D,IAAI,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAE7C,2BAA2B;YAC3B,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;gBAC/B,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBAChC,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,CAAC;gBACvF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5G,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;gBAClC,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACrC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3G,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAChC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAEvC,wBAAwB;YACxB,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAE5C,wBAAwB;YACxB,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;SACjD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC,CAAC;YACxH,MAAM,KAAK,CAAC,CAAC,kCAAkC;SAChD;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACjD,IAAI;gBACF,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;aAC3B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;aAC/C;QACH,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YACnE,OAAO;SACR;QAED,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAClC,oBAAoB;YACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,oCAAoC,MAAM,CAAC,MAAM,aAAa,MAAM,CAAC,KAAK,wBAAwB,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;YAE7I,sBAAsB;YACtB,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC3F,MAAM,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC1E,IAAI,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAE9C,iDAAiD;YACjD,IAAI,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAC7D,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YAEjD,gDAAgD;YAChD,IAAI,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,gBAAgB,CAAC,MAAM,oBAAoB,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAC3D,IAAI,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YAEnD,uBAAuB;YACvB,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;SACrC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;SAC1D;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,MAAoB;QACrD,IAAI;YACF,uCAAuC;YACvC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,OAAO,CAAC,CAAC,MAAM,CAAC;YAC9F,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;YAEpG,0CAA0C;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,eAAe,GAAG;gBACtB,GAAG,eAAe;gBAClB,gBAAgB,EAAE,MAAM,CAAC,eAAe,IAAI,SAAS;gBACrD,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS;gBAChC,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS;gBACjC,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,eAAe;gBAChE,uBAAuB,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAClE,WAAW,EAAE,UAAU;gBACvB,aAAa,EAAE,YAAY,CAAC,QAAQ,EAAE;gBACtC,gBAAgB,EAAE,eAAe,CAAC,QAAQ,EAAE;aAC7C,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;SAC7D;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;SACxD;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAA0B;QACjD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,sBAAsB,CAAC;SAC/B;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACzC,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1E,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/F,OAAO,GAAG,cAAc,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE,GAAG,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,8DAA8D;QAC9D,IAAI,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,GAAG,EAAE,CAAC;YAC5C,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,aAAa,SAAS,eAAe,CAAC;SACtF;QAED,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAA0B;QAC7D,0EAA0E;QAC1E,6DAA6D;QAE7D,+BAA+B;QAC/B,MAAM,qBAAqB,GAAG;YAC5B,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7C,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,SAAS;gBAC9B,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,IAAI,EAAE,MAAM,CAAC,cAAc;aAC5B,CAAC,CAAC,CAAC;SACL,CAAC;QAEF,eAAe;QACf,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,2BAA2B,CAAC;aACpE,OAAO,CAAC,IAAI,EAAE,qBAAqB,CAAC;aACpC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,OAAgB;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAIvD;QACC,4CAA4C;QAC5C,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YACtG,wCAAwC;YACxC,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;aACpB;YAED,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,OAAO,oCAAoC,CAAC;SAC7C;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE3C,WAAW;QACX,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;IACH,CAAC;CACF;AAED,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC"}