import Homey from 'homey';
import { RouterApiClient, RouterStatus, RouterDevice as ApiRouterDevice } from '../../lib/RouterApiClient';

class RouterDevice extends Homey.Device {
  private client: RouterApiClient | null = null;
  private statusUpdateInterval: NodeJS.Timeout | null = null;
  private readonly statusUpdateIntervalMs: number = 60000; // 1 minute

  /**
   * onInit is called when the device is initialized.
   */
  async onInit() {
    this.log('Router device has been initialized');

    // Initialize capabilities
    this.log('Registering capabilities');
    this.registerCapabilities();
    this.log('Capabilities registered successfully');

    // Initialize router client
    this.log('Initializing router client');
    try {
      await this.initRouterClient();
      this.log('Router client initialized successfully');
    } catch (error) {
      this.error('Failed to initialize router client:', error);
    }

    // Start periodic status updates
    this.log('Starting periodic status updates');
    this.startStatusUpdates();
    this.log('Periodic status updates started');
  }

  /**
   * Register capability listeners
   */
  private registerCapabilities() {
    // Register onoff capability (router online status)
    this.registerCapabilityListener('onoff', async (value) => {
      if (value) {
        // Can't turn on a router remotely, so this is a no-op
        return;
      } else {
        // Turn off = restart
        await this.restart();
      }
    });

    // Register button capability for restart
    this.registerCapabilityListener('button.restart', async () => {
      await this.restart();
      // Don't return anything (void)
    });

    // Register guest WiFi toggle capability
    this.registerCapabilityListener('guestwifi', async (value) => {
      await this.setGuestWifi(value);
      // Don't return anything (void)
    });
  }

  /**
   * Initialize the router API client
   */
  private async initRouterClient() {
    try {
      this.log('Getting router settings');
      const settings = this.getSettings();
      let { ip, username, password } = settings;

      // Fallback to default password if not set (for devices paired before password was configured)
      if (!password || password.trim() === '') {
        this.log('Password not set in device settings, using default password for Ruijie X32-PRO');
        password = 'pcs2ass2ADM';

        // Update the device settings with the default password
        await this.setSettings({ ...settings, password });
        this.log('Device settings updated with default password');
      }

      this.log(`Router settings - IP: ${ip}, Username: ${username || 'not set'}, Password: ${password ? '******' : 'not set'}`);

      // Create new client
      this.log('Creating new RouterApiClient instance');
      this.client = new RouterApiClient(ip, username, password);
      this.log('RouterApiClient instance created');

      // Register event listeners
      this.log('Registering event listeners');
      this.client.on('connected', () => {
        this.log('Connected to router');
        this.setAvailable().catch((err) => this.error('Failed to set device available:', err));
        this.setCapabilityValue('onoff', true).catch((err) => this.error('Failed to set onoff capability:', err));
      });

      this.client.on('disconnected', () => {
        this.log('Disconnected from router');
        this.setCapabilityValue('onoff', false).catch((err) => this.error('Failed to set onoff capability:', err));
        this.setUnavailable('Router is offline').catch((err) => this.error('Failed to set device unavailable:', err));
      });

      this.client.on('error', (error) => {
        this.error('Router client error:', error);
      });
      this.log('Event listeners registered');

      // Initialize the client
      this.log('Initializing the client');
      await this.client.init();
      this.log('Client initialized successfully');

      // Update initial status
      this.log('Updating initial status');
      await this.updateStatus();
      this.log('Initial status updated successfully');
    } catch (error) {
      this.error('Failed to initialize router client:', error);
      this.setUnavailable('Failed to connect to router').catch((err) => this.error('Failed to set device unavailable:', err));
      throw error; // Re-throw to propagate the error
    }
  }

  /**
   * Start periodic status updates
   */
  private startStatusUpdates() {
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
    }

    this.statusUpdateInterval = setInterval(async () => {
      try {
        await this.updateStatus();
      } catch (error) {
        this.error('Failed to update status:', error);
      }
    }, this.statusUpdateIntervalMs);
  }

  /**
   * Update router status
   */
  private async updateStatus() {
    if (!this.client) {
      this.log('Cannot update status: Router client is not initialized');
      return;
    }

    try {
      this.log('Getting router status');
      // Get router status
      const status = await this.client.getStatus();
      this.log(`Router status received - Uptime: ${status.uptime}s, Model: ${status.model}, Connected devices: ${status.connectedDevices.length}`);

      // Update capabilities
      this.log('Updating capabilities');
      await this.setCapabilityValue('onoff', true);
      await this.setCapabilityValue('measure_uptime', status.uptime);
      await this.setCapabilityValue('guestwifi', status.guestWifiEnabled);
      this.log('Capabilities updated successfully');

      // Note: Device information settings are label-type (read-only) in app.json
      // so we cannot update them programmatically with setSettings()
      this.log('Device information received - storing for potential future use');

      // Trigger flow for connected devices if changed
      this.log(`Handling ${status.connectedDevices.length} connected devices`);
      await this.handleConnectedDevices(status.connectedDevices);
      this.log('Connected devices handled successfully');

      // Set device available
      this.log('Setting device available');
      await this.setAvailable();
      this.log('Device set to available');
    } catch (error) {
      this.error('Failed to update status:', error);
      this.log('Setting device to unavailable due to error');
      await this.setCapabilityValue('onoff', false);
      await this.setUnavailable('Failed to connect to router');
    }
  }

  /**
   * Handle connected devices updates
   */
  private async handleConnectedDevices(devices: ApiRouterDevice[]) {
    // Note: connected_devices is a label-type (read-only) setting in app.json
    // so we cannot update it programmatically with setSettings()

    // Trigger flow for new devices
    const connectedDevicesToken = {
      count: devices.length,
      devices: JSON.stringify(devices.map(device => ({
        name: device.name || 'Unknown',
        mac: device.mac,
        ip: device.ip,
        type: device.connectionType,
      }))),
    };

    // Trigger flow
    await this.homey.flow.getDeviceTriggerCard('connected_devices_changed')
      .trigger(this, connectedDevicesToken)
      .catch(this.error);
  }

  /**
   * Restart the router
   */
  public async restart() {
    if (!this.client) return false;

    try {
      await this.client.restart();
      return true;
    } catch (error) {
      this.error('Failed to restart router:', error);
      throw error;
    }
  }

  /**
   * Set guest WiFi status
   */
  public async setGuestWifi(enabled: boolean) {
    if (!this.client) return false;

    try {
      await this.client.setGuestWifi(enabled);
      await this.setCapabilityValue('guestwifi', enabled);
      return true;
    } catch (error) {
      this.error('Failed to set guest WiFi:', error);
      throw error;
    }
  }

  /**
   * Check if a device is connected by MAC address
   */
  public async isDeviceConnected(macAddress: string) {
    if (!this.client) return false;

    try {
      return await this.client.isDeviceConnected(macAddress);
    } catch (error) {
      this.error('Failed to check if device is connected:', error);
      throw error;
    }
  }

  /**
   * onSettings is called when the user updates the device's settings.
   * @param {object} event the onSettings event data
   * @param {object} event.oldSettings The old settings object
   * @param {object} event.newSettings The new settings object
   * @param {string[]} event.changedKeys An array of keys changed since the previous version
   * @returns {Promise<string|void>} return a custom message that will be displayed
   */
  async onSettings({ oldSettings, newSettings, changedKeys }: {
    oldSettings: { [key: string]: any };
    newSettings: { [key: string]: any };
    changedKeys: string[];
  }): Promise<string|void> {
    // Check if connection settings have changed
    if (changedKeys.includes('ip') || changedKeys.includes('username') || changedKeys.includes('password')) {
      // Reinitialize client with new settings
      if (this.client) {
        this.client.stopConnectionCheck();
        this.client = null;
      }

      await this.initRouterClient();
      return 'Router connection settings updated';
    }
  }

  /**
   * onDeleted is called when the user deleted the device.
   */
  async onDeleted() {
    this.log('Router device has been deleted');

    // Clean up
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
      this.statusUpdateInterval = null;
    }

    if (this.client) {
      this.client.stopConnectionCheck();
      this.client = null;
    }
  }
}

module.exports = RouterDevice;
